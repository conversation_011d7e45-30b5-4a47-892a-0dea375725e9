import rtconfig
from building import *

# get current directory
cwd = GetCurrentDir()

# The set of source files associated with this SConscript file.
# src = Split("""
# n32g45x_std_periph_driver/src/misc.c
# n32g45x_std_periph_driver/src/n32g45x_adc.c
# n32g45x_std_periph_driver/src/n32g45x_bkp.c
# n32g45x_std_periph_driver/src/n32g45x_can.c
# n32g45x_std_periph_driver/src/n32g45x_comp.c
# n32g45x_std_periph_driver/src/n32g45x_crc.c
# n32g45x_std_periph_driver/src/n32g45x_dac.c
# n32g45x_std_periph_driver/src/n32g45x_dbg.c
# n32g45x_std_periph_driver/src/n32g45x_dma.c
# n32g45x_std_periph_driver/src/n32g45x_dvp.c
# n32g45x_std_periph_driver/src/n32g45x_eth.c
# n32g45x_std_periph_driver/src/n32g45x_exti.c
# n32g45x_std_periph_driver/src/n32g45x_flash.c
# n32g45x_std_periph_driver/src/n32g45x_gpio.c
# n32g45x_std_periph_driver/src/n32g45x_i2c.c
# n32g45x_std_periph_driver/src/n32g45x_iwdg.c
# n32g45x_std_periph_driver/src/n32g45x_opamp.c
# n32g45x_std_periph_driver/src/n32g45x_pwr.c
# n32g45x_std_periph_driver/src/n32g45x_qspi.c
# n32g45x_std_periph_driver/src/n32g45x_rcc.c
# n32g45x_std_periph_driver/src/n32g45x_rtc.c
# n32g45x_std_periph_driver/src/n32g45x_sdio.c
# n32g45x_std_periph_driver/src/n32g45x_spi.c
# n32g45x_std_periph_driver/src/n32g45x_tim.c
# n32g45x_std_periph_driver/src/n32g45x_tsc.c
# n32g45x_std_periph_driver/src/n32g45x_usart.c
# n32g45x_std_periph_driver/src/n32g45x_wwdg.c
# n32g45x_std_periph_driver/src/n32g45x_xfmc.c
# """)
src = Glob('n32g45x_std_periph_driver/src/*.c')
src += [cwd + '/CMSIS/device/system_n32g45x.c']

path = [
    cwd + '/CMSIS/core',
    cwd + '/CMSIS/device',
    cwd + '/n32g45x_std_periph_driver/inc',]

if GetDepend(['RT_USING_BSP_USB']):
    path += [cwd + '/n32g45x_usbfs_driver/inc']
    src  += Glob('n32g45x_usbfs_driver/src/*.c')

CPPDEFINES = ['USE_STDPERIPH_DRIVER']

group = DefineGroup('Libraries', src, depend = [''], CPPPATH = path, CPPDEFINES = CPPDEFINES)

Return('group')

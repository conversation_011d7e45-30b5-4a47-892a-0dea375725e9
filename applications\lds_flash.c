/**
 * @file    lds_flash.c
 * @brief
 * <AUTHOR> @version v1.0.0
 * @date    2024-02-29
 * @par     Copyright
 * Copyright (c) <PERSON><PERSON><PERSON> Lighting 2018-2025. All rights reserved.
 *
 * @par     History
 * 1.Date         : 2024-02-29
 *   Modification : Create file
 */

/* include files ------------------------------------------------------------ */
#include "lds_flash.h"
#include "lds_log.h"

/* macro definition --------------------------------------------------------- */
//#define FLASH_PAGE_SIZE (2048u)
#define WRITE_MIN_BYTE 4 //写入最小字节
#define FLASH_ADDRESS_MIN   0x08000000
#define FLASH_ADDRESS_MAX   0x08080000
/* typedef ------------------------------------------------------------------ */


/* global variables --------------------------------------------------------- */

 
/* local variables ---------------------------------------------------------- */


/* local function declare --------------------------------------------------- */


/* function prototypes ------------------------------------------------------ */

/**
 * @brief Read the data identified with a given address 
 *
 * @param[in] uiAddress The write start address.
 * @param[in] buf	： The write data buffer.
 * @param[in] uiSize  ： The size of write data . should >= 4 byte;
 *
 * @return uiSize
 * @note： erase flash before write !
 */
int halFlashDataWrite(uint32_t uiAddress, const uint8_t *buf, uint32_t uiSize)
{
    uint16_t i;
    uint16_t ret;
    uint32_t write_data;
    uint32_t read_data;

    if(uiAddress < FLASH_ADDRESS_MIN || uiAddress > FLASH_ADDRESS_MAX){
        //WARN_LOG("write flash address error !\r\n");
        return 1;
    }

    /*FLASH写入*/
    FLASH_ClearFlag(FLASH_FLAG_PGERR | FLASH_FLAG_PVERR | FLASH_FLAG_WRPERR | FLASH_FLAG_EOP | FLASH_FLAG_EVERR);
    FLASH_Unlock(); 

    for (i = 0; i < uiSize; i += 4, buf += 4, uiAddress += 4)
    {
        memcpy(&write_data, buf, 4); // 用以保证FLASH_ProgramWord的write_data是内存首地址对齐
        FLASH_ProgramWord(uiAddress, write_data);
        read_data = *(__IO uint32_t *)(uiAddress);

        /*校验写入数据*/
        if (read_data != write_data)
        {
            FLASH_Lock(); 
            //WARN_LOG("FLASH data vertify error!\r\n");
            return 1;
        }
        else
        {
            // FLash操作可能非常耗时，如果有看门狗需要喂狗
        }
    }
    FLASH_Lock();
    return 0;
}

/**
 * @brief Read the data identified with a given address
 *
 * @param[in] uiAddress The read start address.
 * @param[out] buf	： The read data buffer.
 * @param[in] uiSize  ： The size of read data.
 *
 * @return E_TRUE
 */
int halFlashDataRead(uint32_t uiAddress, uint8_t *buf, uint32_t uiSize)
{
    uint32_t i;

    if(uiAddress < FLASH_ADDRESS_MIN || uiAddress > FLASH_ADDRESS_MAX){
        //WARN_LOG("write flash address error !\r\n");
        return 1;
    }

    for (i = 0; i < uiSize; i++, uiAddress++, buf++)
    {
        *buf = *(__IO uint8_t *) uiAddress;
    }

    return 0;
}

/**
 * @brief Erase the data identified with a given addres and size.
 *
 * @param[in] uiAddress The erase start address.
 * @param[in] uiSize The erase data size.
 *
 * @return E_TRUE
 */
int halFlashDataErase(uint32_t uiAddress, uint32_t uiSize)
{
    uint16_t erase_pages_num, i;
    FLASH_STS flash_ret;

    if(uiAddress < FLASH_ADDRESS_MIN || uiAddress > FLASH_ADDRESS_MAX){
        //WARN_LOG("flash address error !\r\n");
        return 1;
    }

    if (uiAddress % FLASH_PAGE_SIZE) 
    {
        //WARN_LOG("flash address not page start !\r\n");
        return 1;
    }

    // 计算要擦除的页数量
    erase_pages_num = uiSize / FLASH_PAGE_SIZE;
    if (uiSize % FLASH_PAGE_SIZE != 0)
    {
        erase_pages_num++;
    }

    FLASH_ClearFlag(FLASH_FLAG_PGERR | FLASH_FLAG_PVERR | FLASH_FLAG_WRPERR | FLASH_FLAG_EOP | FLASH_FLAG_EVERR);
    FLASH_Unlock();

    // 一次擦除一个扇区
    uint32_t page_address;
    for (i = 0; i < erase_pages_num; i++)
    {
        page_address = uiAddress + (FLASH_PAGE_SIZE * i);
        flash_ret = FLASH_EraseOnePage(page_address);
        if(flash_ret != FLASH_COMPL){
            FLASH_Lock();  
            //WARN_LOG("FLASH erase error%d \r\n",flash_ret);
            return 1;
        }
    }

    FLASH_Lock();

    return 0;
}

/**
 * @brief Flash initialization
 *
 * @return E_TRUE
 */
int halFlashInit(void)
{
    FLASH_SetLatency(FLASH_LATENCY_4);
    return 0;
}


// 读取1个字长度的内容 faddr为起始地址
uint32_t FLASH_ReadWord(uint32_t faddr)
{
    return *(__IO uint32_t *)faddr;
}

uint8_t FLASH_ReadByte(uint32_t faddr)
{
    return *(__IO uint8_t *)faddr;
}

uint8_t FLASH_Read_Byte(uint32_t ReadAddr)
{
    uint8_t data;
    data = (uint8_t)(FLASH_ReadWord(ReadAddr));
    return data;
}
// 读取n个1字节长度的内容 faddr为起始地址,长度>4
void FLASH_Read_Nnum_Word(uint32_t ReadAddr, uint32_t *pBuffer, uint16_t NumToRead)
{
    uint16_t i;

    for (i = 0; i < NumToRead; i++)
    {
        pBuffer[i] = FLASH_ReadWord(ReadAddr);
        ReadAddr += 4;
    }
}
// 读取n个1字节长度的内容 faddr为起始地址,长度>4
FLASH_STS FLASH_Read_Nnum_Byte(uint32_t ReadAddr, uint8_t *pBuffer, uint16_t NumToRead)
{
    uint16_t i;
    uint16_t numword;
    uint32_t readword;

    numword = NumToRead / 4;

    for (i = 0; i < numword; i++)
    {
        readword = FLASH_ReadWord(ReadAddr);

        pBuffer[4 * i] = (uint8_t)(readword);
        pBuffer[4 * i + 1] = (uint8_t)(readword >> 8);
        pBuffer[4 * i + 2] = (uint8_t)(readword >> 16);
        pBuffer[4 * i + 3] = (uint8_t)(readword >> 24);

        ReadAddr += 4;
    }
    return FLASH_COMPL;
}
/*----------------------------------------------------------------------------*/

/**************************** END OF FILE *************************************/

# N32G452

## 简介
| 硬件      | 描述          |
| --------- | ------------- |
| 芯片型号  | N32G452VE  |
| CPU       | ARM Cortex M4 |
| 主频      | 144M          |
| 片内SRAM  | 144K |
| 片内FLASH | 512K         |


## 使用说明
使用rt-thread实时系统，编译需要先安装env
### windows
#### 方式一（需要联网环境）：
```shell
git clone --recursive --depth 1 https://gitee.com/mirrors_RT-Thread/env-windows.git
```
双击 env.exe 进入 env 环境，进行首次使用环境初始化。
首次使用需要联网安装pip依赖，请等待依赖安装完成，若安装失败：请手动删除 env-windows 目录下的 .venv 目录，再次打开 env.exe 进行重新依赖安装即可。

#### 方式二（离线环境）：
下载并解压 [env-windows-v2.0.0-venv.7z](https://github.com/RT-Thread/env-windows/releases/download/v2.0.0/env-windows-v2.0.0-venv.7z) 到C盘根目录（目录结构为：C:\env-windows），双击 env.exe 进入 env 环境，进行首次使用环境初始化。
必须解压到C盘根目录，结构为：C:\env-windows\env.exe...

### 编译
打开env环境后，切换到项目根目录执行下面命令
```shell
scons
```

## 注意事项

## 参考
[rt-thread文档](https://www.rt-thread.org/document/site/#/rt-thread-version/rt-thread-standard/README)
[env文档](https://www.rt-thread.org/document/site/#/development-tools/env/env)


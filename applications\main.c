/*
 * Copyright (c) 2006-2023, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2015-07-29     Arda.Fu      first implementation
 */
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <stdbool.h>
#include <n32g45x.h>
#include "lds_log.h"
#include "lds_ymodem.h"
#include "lds_flash.h"

#define MODEL_ID "AUX-EDUAudioProcessor-0001-boot"

// BootInfo.h
#define BOOT_INFO_MAGIC   0xA5A5A5A5  // 魔术字，用于判断该区域是否有效

// APP分区定义
#define APP1_START_ADDR     0x08008000
#define APP2_START_ADDR     0x08040000

typedef enum {
    APP_TARGET_APP1 = 0x01,
    APP_TARGET_APP2 = 0x02
} LDS_APP_TARGET_E;
 
typedef struct {
    uint32_t magic;           // 魔术字，用于校验
    LDS_APP_TARGET_E boot_target;  // 启动目标：APP1 或 APP2
    uint32_t app1_crc32;        // APP1 固件的 CRC32 校验值
    uint32_t app1_size;         // APP1 固件的大小
    char app1_version[8];      // APP1 版本号
    uint32_t app2_crc32;        // APP2 固件的 CRC32 校验值
    uint32_t app2_size;       // APP2 固件的大小
    char app2_version[8];      // APP2 版本号
    uint32_t crc32;           // boot 信息检验值
} BootInfo_t;

extern uint32_t ldsUtilCheckCrc32(const uint32_t *data, size_t len);
 
void hw_us_delay(uint32_t us)
{
    if (us == 0) {
         return; // 延时0微秒直接返回
     }
       // 禁用SysTick，清除所有设置
    SysTick->CTRL = 0;
    // 设置SysTick重载值为最大值 (24位计数器)，使其自由运行
    SysTick->LOAD = 0xFFFFFF;
    // 清除当前计数器值
    SysTick->VAL = 0;
    // 配置SysTick使用HCLK作为时钟源，并使能计数器
    SysTick->CTRL = SysTick_CTRL_CLKSOURCE_Msk | SysTick_CTRL_ENABLE_Msk;
 
    // 计算每微秒对应的SysTick节拍数
    // 确保 SystemCoreClock 是正确的CPU频率（HCLK）
    uint32_t ticks_per_us = SystemCoreClock / 1000000UL; // 使用UL确保是无符号长整型除法
    if (ticks_per_us == 0) {
        // 如果SystemCoreClock太低或未初始化，ticks_per_us可能为0
        // 在这种情况下，无法进行精确延时，可以考虑报错或返回。
        // 这里简单地返回，避免除以0或无限循环。
        return;
    }
 
    // 计算总共需要等待的节拍数
    uint32_t total_ticks = us * ticks_per_us;
 
    // 获取延时开始时的SysTick计数器值
    uint32_t start_tick = SysTick->VAL;
    uint32_t elapsed_ticks = 0;
    uint32_t current_tick;
 
    // 忙等待循环
    while (elapsed_ticks < total_ticks) {
        current_tick = SysTick->VAL;
 
        // 判断是否发生回绕 (从 0xFFFFFF 变为 0)
        if (current_tick <= start_tick) {
            // 没有发生回绕，直接计算差值
            elapsed_ticks = start_tick - current_tick;
        } else {
            // 发生回绕，需要加上从 0xFFFFFF 到 0 这一段的节拍数
            // SysTick->LOAD + 1 是计数器的总范围 (例如 0xFFFFFF + 1)
            elapsed_ticks = (SysTick->LOAD + 1 - current_tick) + start_tick;
        }
    }
    SysTick->CTRL&=~SysTick_CTRL_ENABLE_Msk;       //close the count
    SysTick->VAL =0;       //clear timer value  
}
uint32_t ldsUtilReverseBits(uint32_t dword, size_t len)
{
    if (len == 0 || len > sizeof(dword))
    {
        printf("Invalid length for bit reversal: %zu bytes. Must be between 1 and %zu.", len, sizeof(dword));
        return 0;
    }

    switch (len)
    {
        case 1: // 8-bit reversal
            dword = ((dword >> 1) & 0x55) | ((dword & 0x55) << 1);
            dword = ((dword >> 2) & 0x33) | ((dword & 0x33) << 2);
            dword = ((dword >> 4) & 0x0F) | ((dword & 0x0F) << 4);
            return dword;

        case 2: // 16-bit reversal
            dword = ((dword >> 1) & 0x5555) | ((dword & 0x5555) << 1);
            dword = ((dword >> 2) & 0x3333) | ((dword & 0x3333) << 2);
            dword = ((dword >> 4) & 0x0F0F) | ((dword & 0x0F0F) << 4);
            dword = ((dword >> 8) & 0x00FF) | ((dword & 0x00FF) << 8);
            return dword;
        
        case 4: // 32-bit reversal
            dword = ((dword >> 1) & 0x55555555) | ((dword & 0x55555555) << 1);
            dword = ((dword >> 2) & 0x33333333) | ((dword & 0x33333333) << 2);
            dword = ((dword >> 4) & 0x0F0F0F0F) | ((dword & 0x0F0F0F0F) << 4);
            dword = ((dword >> 8) & 0x00FF00FF) | ((dword & 0x00FF00FF) << 8);
            dword = (dword >> 16) | (dword << 16);
            return dword;

        default: // Fallback for other cases (e.g., 3 bytes).
        {
            const size_t num_bits = len * 8;
            uint32_t reversed_dword = 0;
            for (size_t i = 0; i < num_bits; i++)
            {
                reversed_dword <<= 1;
                reversed_dword |= (dword & 1);
                dword >>= 1;
            }
            return reversed_dword;
        }
    }
}
//crc32-mpeg-2
uint32_t ldsUtilCheckCrc32(const uint32_t *data, size_t len)
{
    uint32_t index = 0;
    uint32_t crc = 0;
    if (data == NULL || len == 0) {
        printf("Invalid data or size");
        return 0;
    }

    CRC32_ResetCrc();
    for (index = 0; index < len; index++) {
        CRC->CRC32DAT = ldsUtilReverseBits(data[index], sizeof(data[index]));
    }
    crc = (CRC->CRC32DAT);
    crc = ldsUtilReverseBits(crc, sizeof(crc));
    return (crc ^ 0xFFFFFFFF);
}

static uint32_t test_buf[2] = {0x12345678, 0x9ABCDEF0};

/**
 * @brief 校验APP区域的完整性
 * @param app_addr APP起始地址
 * @param app_size APP大小
 * @param expected_crc32 期望的CRC32值
 * @return true 校验成功, false 校验失败
 */
bool ldsVerifyAppIntegrity(uint32_t app_addr, uint32_t app_size, uint32_t expected_crc32)
{
    if (app_size == 0 || app_size % 4 != 0) {
        printf("Invalid app size: %u\n", app_size);
        return false;
    }
    
    uint32_t calculated_crc = ldsUtilCheckCrc32((const uint32_t*)app_addr, app_size / 4);
    printf("App addr: 0x%08X, size: %u, expected CRC: 0x%08X, calculated CRC: 0x%08X\n", 
           app_addr, app_size, expected_crc32, calculated_crc);
    
    return (calculated_crc == expected_crc32);
}

/**
 * @brief 跳转到指定APP
 * @param app_addr APP起始地址
 */
void ldsJumpToApp(uint32_t app_addr)
{
    uint32_t stack_ptr = *((uint32_t*)app_addr);
    uint32_t reset_handler = *((uint32_t*)(app_addr + 4));
    
    printf("Jumping to APP at 0x%08X, stack: 0x%08X, reset: 0x%08X\n", 
           app_addr, stack_ptr, reset_handler);
    
    // 检查栈指针和复位向量的有效性
    if ((stack_ptr & 0xFFF00000) != 0x20000000 || 
        (reset_handler & 0xFFF00000) != 0x08000000) {
        printf("Invalid APP vectors\n");
        return;
    }
    
    // 禁用所有中断
    __disable_irq();
    
    // 设置栈指针
    __set_MSP(stack_ptr);
    
    // 跳转到APP
    ((void(*)(void))reset_handler)();
}

void delay_ms(uint32_t ms) {
    for (uint32_t i = 0; i < ms; i++) {
        hw_us_delay(1000); // 1毫秒 = 1000微秒
    }
}

int main(void)
{
    uint32_t count = 0;
    BootInfo_t* boot_info;
    bool app1_valid = false;
    bool app2_valid = false;
    
    /* Enable CRC clock */
    RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_CRC, ENABLE);

    ldsUartLogInit();
    ldsYmodemInit();

    printf("Starting %s process\n", MODEL_ID);
    uint32_t crc32 = ldsUtilCheckCrc32(test_buf, sizeof(test_buf) / 4);
    printf("test_buf crc32: 0x%08X\n", crc32);
    // 读取启动信息
    boot_info = (BootInfo_t*)BOOT_INFO_ADDR;
    
    // 校验boot info的完整性
    if (boot_info->magic == BOOT_INFO_MAGIC) {
        uint32_t boot_info_crc = ldsUtilCheckCrc32((const uint32_t*)boot_info, 
                                                  (sizeof(BootInfo_t) - 4) / 4);
        if (boot_info_crc == boot_info->crc32) {
            printf("Boot info valid, target: %s\n", 
                   (boot_info->boot_target == APP_TARGET_APP1) ? "APP1" : "APP2");
            
            // 校验APP1
            if (boot_info->app1_size > 0) {
                app1_valid = ldsVerifyAppIntegrity(APP1_START_ADDR, 
                                                  boot_info->app1_size, 
                                                  boot_info->app1_crc32);
                printf("APP1 verification: %s (version: %s)\n", 
                       app1_valid ? "PASS" : "FAIL", boot_info->app1_version);
            }
            
            // 校验APP2
            if (boot_info->app2_size > 0) {
                app2_valid = ldsVerifyAppIntegrity(APP2_START_ADDR, 
                                                  boot_info->app2_size, 
                                                  boot_info->app2_crc32);
                printf("APP2 verification: %s (version: %s)\n", 
                       app2_valid ? "PASS" : "FAIL", boot_info->app2_version);
            }
            
            // 根据启动目标和校验结果决定启动哪个APP
            if (boot_info->boot_target == APP_TARGET_APP1 && app1_valid) {
                printf("Booting APP1...\n");
                delay_ms(100);
                ldsJumpToApp(APP1_START_ADDR);
            } else if (boot_info->boot_target == APP_TARGET_APP2 && app2_valid) {
                printf("Booting APP2...\n");
                delay_ms(100);
                ldsJumpToApp(APP2_START_ADDR);
            } else if (app1_valid) {
                printf("Target APP invalid, fallback to APP1...\n");
                delay_ms(100);
                ldsJumpToApp(APP1_START_ADDR);
            } else if (app2_valid) {
                printf("Target APP invalid, fallback to APP2...\n");
                delay_ms(100);
                ldsJumpToApp(APP2_START_ADDR);
            }
        } else {
            printf("Boot info CRC error: expected 0x%08X, got 0x%08X\n", 
                   boot_info->crc32, boot_info_crc);
        }
    } else {
        printf("Boot info magic error: 0x%08X\n", boot_info->magic);
    }
    
    // 如果到达这里，说明两个APP区都校验失败，进入升级模式
    printf("Both APP regions invalid, entering YModem upgrade mode...\n");
    printf("Please send firmware file via YModem protocol\n");
    printf("Expected filename format: host-V1.00.02-23abcdef.bin\n");
    
    while (1) {
        lds_ymodem_result_t result = ldsYmodemUpgrade();
        
        if (result == YMODEM_OK) {
            printf("Upgrade completed successfully, rebooting...\n");
            delay_ms(1000);
            NVIC_SystemReset();
        } else {
            printf("Upgrade failed with error %d, retrying...\n", result);
            delay_ms(2000);
        }
    }
}

/**
 * @file    lds_log.h
 * @brief
 * <AUTHOR> @version v1.0.0
 * @date
 * 
 * @copyright Copyright (c) <PERSON><PERSON><PERSON> ~. All rights reserved.
 */
/* prevent from redefinition ------------------------------------------------ */
#ifndef LDS_LOG_H_
#define LDS_LOG_H_

#ifdef __cplusplus
extern "C" {
#endif

/* needed include files ----------------------------------------------------- */
#include <string.h>
#include <stdint.h>
#include <stdio.h>

#include "n32g45x.h"
#include "system_n32g45x.h"

/* macro definition --------------------------------------------------------- */


/* typedef ------------------------------------------------------------------ */


/* global variables declare ------------------------------------------------- */


/* global function declare -------------------------------------------------- */

int ldsUartLogInit(void);
int ldsUsartLogSendString(char *pStr);
int ldsUsartLogOutput(char *fmt, ...);

#ifdef __cplusplus
}
#endif

#endif

/**************************** END OF FILE *************************************/

/**
 * @file lds_uart.h
 * @brief LDS UART Driver Header File for RT-Thread Applications Layer
 * @details This header file provides UART communication functionality with DMA support,
 *          callback registration, and message queue-based data processing for RT-Thread.
 * <AUTHOR> Team
 * @date 2025-07-09
 * @version 1.0
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * Features:
 * - Multi-UART support (up to 7 UART interfaces)
 * - DMA-based reception with polling transmission
 * - Callback-based data processing
 * - Message queue for efficient data handling
 * - Thread-safe operations
 * - RT-Thread integration
 */

#ifndef __APPLICATIONS_LDS_UART_H__
#define __APPLICATIONS_LDS_UART_H__
#if 0
/* ================================ Includes ================================ */
#include <rtthread.h>
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ Macros ================================== */

/* ================================ Type Definitions ======================== */

/**
 * @brief UART interface index enumeration
 * @details Defines the available UART interface indices for the LDS UART driver
 */
typedef enum {
    LDS_UART_INDEX_1 = 0,    /**< UART interface 1 */
    LDS_UART_INDEX_2,        /**< UART interface 2 */
    LDS_UART_INDEX_3,        /**< UART interface 3 */
    LDS_UART_INDEX_4,        /**< UART interface 4 */
    LDS_UART_INDEX_5,        /**< UART interface 5 */
    LDS_UART_INDEX_6,        /**< UART interface 6 */
    LDS_UART_INDEX_7,        /**< UART interface 7 */
    LDS_UART_INDEX_MAX,      /**< Maximum UART index (boundary check) */
} LDS_UART_INDEX_E;

/**
 * @brief UART callback function type
 * @details Callback function prototype for handling received UART data
 *
 * @param dev RT-Thread device handle
 * @param data Pointer to received data buffer
 * @param size Size of received data in bytes
 * @return int Return code (0 for success, negative for error)
 */
typedef int (*ldsUartCb_t)(rt_device_t dev, const uint8_t* data, rt_size_t size);

/* ================================ Function Declarations =================== */

/**
 * @brief Initialize UART interface with callback
 * @details Initializes the specified UART interface with DMA reception and
 *          registers a callback function for data processing
 *
 * @param uart_name Name of the UART device (e.g., "uart1", "uart2")
 * @param index UART interface index from LDS_UART_INDEX_E
 * @param cb Callback function to handle received data
 * @return rt_device_t RT-Thread device handle on success, RT_NULL on failure
 *
 * @note The UART will be opened with RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_DMA_RX flags
 * @note This function automatically calls internal initialization if not already done
 *
 * @example
 * @code
 * static int myUartCallback(rt_device_t dev, const char* data, rt_size_t size) {
 *     // Process received data
 *     rt_kprintf("Received %d bytes: %.*s\n", size, size, data);
 *     return 0;
 * }
 *
 * rt_device_t result = ldsUartInit("uart1", LDS_UART_INDEX_1, myUartCallback);
 * if (result == RT_NULL) {
 *     rt_kprintf("UART initialization failed\n");
 * }
 * @endcode
 */
rt_device_t ldsUartInit(const char* uart_name, LDS_UART_INDEX_E index, ldsUartCb_t cb);

#ifdef __cplusplus
}
#endif
#endif
#endif /* __APPLICATIONS_LDS_UART_H__ */
/**
 * @file    lds_hal_flash.h
 * @brief
 * <AUTHOR> @version v1.0.0
 * @date    2021-10-14
 * @par     Copyright
 * Copyright (c) Lee<PERSON>son Lighting 2018-2025. All rights reserved.
 *
 * @par     History
 * 1.Date         : 2021-10-14
 *   Modification : Create file
 */

/* prevent from redefinition ------------------------------------------------ */
#ifndef LDS_HAL_FLASH_H_
#define LDS_HAL_FLASH_H_

/* needed include files ----------------------------------------------------- */
#include "n32g45x.h"
#include "system_n32g45x.h"

/* macro definition --------------------------------------------------------- */

#if 1
#define FLASH_BOOTLOADER_ADDR_START 0x08000000      // Bootloader起始地址，10KB
#define FLASH_APP1_ADDR             0x08002800      // 第一个应用程序起始地址(存放在FLASH), 220KB
#define FLASH_APP1_ADDR_END         0x080397FF
#define FLASH_APP2_ADDR             0x08039800      // 第二个应用程序起始地址(存放在FLASH), 220KB
#define FLASH_APP2_ADDR_END         0x080767FF
#define FLASH_OTA_FLAG_ADDR         0x08076800      // OTA标志， 2KB
#define FLASH_OTA_FLAG_ADDR_END     0x08076FFF
#define FLASH_RESERVE_ADDR          0x08077000      // 保留20KB
#define FLASH_RESERVE_ADDR_END      0x0807BFFF
#define FLASH_FLASH_DB_ADDR         0x0807C000      // FlashDB  16K

#define FLASH_APP_SIZE              0x37000         // 220KB
#define FLASH_PAGE_SIZE             2048
#else
#define FLASH_BOOTLOADER_ADDR_START 0x08000000      // Bootloader起始地址，60KB
#define FLASH_APP1_ADDR             0x0800F000      // 第一个应用程序起始地址(存放在FLASH), 170KB
#define FLASH_APP1_ADDR_END         0x080397FF
#define FLASH_APP2_ADDR             0x08039800      // 第二个应用程序起始地址(存放在FLASH), 220KB
#define FLASH_APP2_ADDR_END         0x080767FF
#define FLASH_OTA_FLAG_ADDR         0x08076800      // OTA标志， 2KB
#define FLASH_OTA_FLAG_ADDR_END     0x08076FFF
#define FLASH_RESERVE_ADDR          0x08077000      // 保留20KB
#define FLASH_RESERVE_ADDR_END      0x0807BFFF
#define FLASH_FLASH_DB_ADDR         0x0807C000      // FlashDB  16K

#define FLASH_APP_SIZE              0x2A800         // 170KB
#define FLASH_PAGE_SIZE             2048
#endif
#define BOOT_INFO_ADDR    FLASH_BOOTLOADER_ADDR_START + 0x100000 - 0x1000

/* typedef ------------------------------------------------------------------ */


/* global variables declare ------------------------------------------------- */


/* global function declare -------------------------------------------------- */
/**
 * @brief Flash initialization
 *
 * @return E_TRUE
 */
int halFlashInit(void);
/**
 * @brief Read the data identified with a given address
 *
 * @param[in] uiAddress The write start address.
 * @param[in] buf	： The write data buffer.
 * @param[in] uiSize  ： The size of write data.
 *
 * @return E_TRUE
 */
int halFlashDataWrite(uint32_t uiAddress, const uint8_t *buf, uint32_t uiSize);
/**
 * @brief Read the data identified with a given address
 *
 * @param[in] uiAddress The read start address.
 * @param[out] buf	： The read data buffer.
 * @param[in] uiSize  ： The size of read data.
 *
 * @return E_TRUE
 */
int halFlashDataRead(uint32_t uiAddress, uint8_t *buf, uint32_t uiSize);
/**
 * @brief Erase the data identified with a given addres and size.
 *
 * @param[in] uiAddress The erase start address.
 * @param[in] uiSize The erase data size.
 *
 * @return
 */
int halFlashDataErase(uint32_t uiAddress, uint32_t uiSize);

void FLASH_Read_Nnum_Word(uint32_t ReadAddr, uint32_t *pBuffer, uint16_t NumToRead);
FLASH_STS FLASH_Read_Nnum_Byte(uint32_t ReadAddr, uint8_t *pBuffer, uint16_t NumToRead);

#endif //LDS_HAL_FLASH_H_
/**************************** END OF FILE *************************************/
